#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def fix_download_records():
    """修复下载记录，为没有用户ID的记录分配默认用户ID"""
    db_path = 'data/file_share_system.db'
    
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取admin用户的ID
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        if not admin_user:
            print("找不到admin用户")
            return
        
        admin_user_id = admin_user[0]
        print(f"找到admin用户，ID: {admin_user_id}")
        
        # 查看当前没有用户ID的下载记录
        cursor.execute("SELECT COUNT(*) FROM download_records WHERE user_id IS NULL")
        null_user_count = cursor.fetchone()[0]
        print(f"找到 {null_user_count} 条没有用户ID的下载记录")
        
        if null_user_count > 0:
            # 为这些记录分配admin用户ID
            cursor.execute("UPDATE download_records SET user_id = ? WHERE user_id IS NULL", (admin_user_id,))
            updated_count = cursor.rowcount
            print(f"已更新 {updated_count} 条记录的用户ID")
            
            # 提交更改
            conn.commit()
            print("数据库更新完成")
        else:
            print("所有下载记录都已有用户ID")
        
        # 验证更新结果
        cursor.execute("SELECT COUNT(*) FROM download_records WHERE user_id = ?", (admin_user_id,))
        admin_records_count = cursor.fetchone()[0]
        print(f"admin用户现在有 {admin_records_count} 条下载记录")
        
        # 显示最近的几条记录
        cursor.execute("""
            SELECT id, user_id, download_type, zip_filename, created_at 
            FROM download_records 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT 5
        """, (admin_user_id,))
        
        recent_records = cursor.fetchall()
        print("\n最近的5条下载记录:")
        for record in recent_records:
            print(f"  ID: {record[0]}, 用户ID: {record[1]}, 类型: {record[2]}, 文件名: {record[3]}, 时间: {record[4]}")
        
        conn.close()
        
    except Exception as e:
        print(f'修复下载记录时出错: {e}')

if __name__ == '__main__':
    fix_download_records()
