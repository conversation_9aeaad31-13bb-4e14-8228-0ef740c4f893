#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database():
    """检查数据库中的下载记录"""
    db_path = 'data/file_share_system.db'
    
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    print(f'数据库文件存在: {db_path}')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查下载记录表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='download_records'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print('下载记录表存在')
            
            # 检查表结构
            cursor.execute('PRAGMA table_info(download_records)')
            columns = cursor.fetchall()
            print('表结构:')
            for col in columns:
                print(f'  {col[1]} {col[2]}')
            
            # 检查记录数量
            cursor.execute('SELECT COUNT(*) FROM download_records')
            count = cursor.fetchone()[0]
            print(f'下载记录数量: {count}')
            
            # 显示前几条记录
            if count > 0:
                cursor.execute('SELECT id, user_id, download_type, zip_filename, created_at FROM download_records LIMIT 5')
                records = cursor.fetchall()
                print('前5条记录:')
                for record in records:
                    print(f'  ID: {record[0]}, 用户ID: {record[1]}, 类型: {record[2]}, 文件名: {record[3]}, 创建时间: {record[4]}')
        else:
            print('下载记录表不存在')
        
        # 检查用户表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        users_table_exists = cursor.fetchone()
        
        if users_table_exists:
            cursor.execute('SELECT COUNT(*) FROM users')
            user_count = cursor.fetchone()[0]
            print(f'用户数量: {user_count}')
            
            if user_count > 0:
                cursor.execute('SELECT id, username FROM users LIMIT 3')
                users = cursor.fetchall()
                print('用户列表:')
                for user in users:
                    print(f'  ID: {user[0]}, 用户名: {user[1]}')
        
        conn.close()
        
    except Exception as e:
        print(f'检查数据库时出错: {e}')

if __name__ == '__main__':
    check_database()
