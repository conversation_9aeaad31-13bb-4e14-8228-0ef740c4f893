<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载记录测试页面</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/download-records.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .test-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .status-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .download-records-view {
            min-height: 400px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-download"></i> 下载记录功能测试</h1>
            <p>测试用户下载记录的加载和显示功能</p>
        </div>

        <div class="test-actions">
            <button class="btn btn-primary" onclick="testLogin()">
                <i class="fas fa-sign-in-alt"></i>
                测试登录
            </button>
            <button class="btn btn-secondary" onclick="testDownloadRecords()">
                <i class="fas fa-download"></i>
                加载下载记录
            </button>
            <button class="btn btn-secondary" onclick="clearResults()">
                <i class="fas fa-trash"></i>
                清空结果
            </button>
        </div>

        <div class="status-info" id="status-info">
            <strong>状态:</strong> <span id="status-text">等待操作...</span>
        </div>

        <!-- 下载记录视图容器 -->
        <div class="download-records-view" id="download-records-view">
            <div style="text-align: center; padding: 50px; color: #666;">
                <i class="fas fa-info-circle" style="font-size: 48px; margin-bottom: 20px;"></i>
                <p>点击上方按钮开始测试下载记录功能</p>
            </div>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>
    <script src="js/file-manager.js"></script>

    <script>
        // 测试用的全局变量
        let testToken = null;
        let testFileManager = null;

        // 更新状态显示
        function updateStatus(message, type = 'info') {
            const statusText = document.getElementById('status-text');
            const statusInfo = document.getElementById('status-info');
            
            statusText.textContent = message;
            
            // 更新样式
            statusInfo.className = 'status-info';
            if (type === 'success') {
                statusInfo.style.background = '#e8f5e8';
                statusInfo.style.borderColor = '#4caf50';
            } else if (type === 'error') {
                statusInfo.style.background = '#ffebee';
                statusInfo.style.borderColor = '#f44336';
            } else {
                statusInfo.style.background = '#e3f2fd';
                statusInfo.style.borderColor = '#2196f3';
            }
        }

        // 测试登录功能
        async function testLogin() {
            updateStatus('正在测试登录...', 'info');
            
            try {
                const response = await fetch('http://localhost:8086/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const result = await response.json();
                console.log('登录响应:', result);

                if (result.success) {
                    testToken = result.token;
                    
                    // 保存到localStorage，模拟正常登录
                    localStorage.setItem('fileShareAuth', JSON.stringify({
                        token: result.token,
                        user: result.user,
                        serverUrl: 'http://localhost:8086'
                    }));

                    updateStatus(`登录成功！用户: ${result.user.username}`, 'success');
                } else {
                    updateStatus(`登录失败: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('登录测试失败:', error);
                updateStatus(`登录测试失败: ${error.message}`, 'error');
            }
        }

        // 测试下载记录功能
        async function testDownloadRecords() {
            if (!testToken) {
                updateStatus('请先测试登录！', 'error');
                return;
            }

            updateStatus('正在加载下载记录...', 'info');

            try {
                // 创建文件管理器实例
                if (!testFileManager) {
                    testFileManager = new FileManager();
                }

                // 获取下载记录
                const records = await testFileManager.getDownloadRecords();
                console.log('获取到的下载记录:', records);

                if (records && records.length > 0) {
                    // 渲染下载记录
                    testFileManager.renderDownloadRecords(records);
                    updateStatus(`成功加载 ${records.length} 条下载记录`, 'success');
                } else {
                    updateStatus('没有找到下载记录', 'info');
                    
                    // 显示空状态
                    const container = document.getElementById('download-records-view');
                    container.innerHTML = `
                        <div style="text-align: center; padding: 50px; color: #666;">
                            <i class="fas fa-download" style="font-size: 48px; margin-bottom: 20px;"></i>
                            <h3>暂无下载记录</h3>
                            <p>您还没有下载过任何文件</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('加载下载记录失败:', error);
                updateStatus(`加载下载记录失败: ${error.message}`, 'error');
            }
        }

        // 清空结果
        function clearResults() {
            const container = document.getElementById('download-records-view');
            container.innerHTML = `
                <div style="text-align: center; padding: 50px; color: #666;">
                    <i class="fas fa-info-circle" style="font-size: 48px; margin-bottom: 20px;"></i>
                    <p>点击上方按钮开始测试下载记录功能</p>
                </div>
            `;
            updateStatus('结果已清空', 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('下载记录测试页面已加载');
            updateStatus('页面已加载，可以开始测试', 'success');
        });
    </script>
</body>
</html>
