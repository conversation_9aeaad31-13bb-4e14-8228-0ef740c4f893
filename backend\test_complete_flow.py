#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_complete_download_records_flow():
    """测试完整的下载记录流程"""
    
    base_url = "http://localhost:8086/api"
    
    print("=== 测试下载记录完整流程 ===\n")
    
    # 1. 登录
    print("1. 用户登录...")
    login_response = requests.post(f"{base_url}/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return
    
    login_data = login_response.json()
    print(f"登录响应: {json.dumps(login_data, indent=2, ensure_ascii=False)}")

    if not login_data.get('success'):
        print(f"❌ 登录失败: {login_data.get('error')}")
        return

    # 处理不同的响应格式
    if 'data' in login_data:
        token = login_data['data']['token']
        user_id = login_data['data']['user']['id']
    else:
        token = login_data.get('token')
        user_id = login_data.get('user', {}).get('id')

    if not token:
        print(f"❌ 无法获取token")
        return

    print(f"✅ 登录成功，用户ID: {user_id}, Token: {token[:20]}...")
    
    # 2. 获取下载记录
    print("\n2. 获取下载记录...")
    headers = {"Authorization": f"Bearer {token}"}
    
    records_response = requests.get(f"{base_url}/download/records", headers=headers)
    
    if records_response.status_code != 200:
        print(f"❌ 获取下载记录失败: {records_response.status_code}")
        print(f"响应内容: {records_response.text}")
        return
    
    records_data = records_response.json()
    print(f"✅ API响应: {json.dumps(records_data, indent=2, ensure_ascii=False)}")
    
    if records_data.get('success'):
        records = records_data.get('records', [])
        total = records_data.get('total', 0)
        print(f"✅ 成功获取 {len(records)} 条下载记录（总计 {total} 条）")
        
        # 显示前几条记录的详细信息
        for i, record in enumerate(records[:3]):
            print(f"\n记录 {i+1}:")
            print(f"  ID: {record.get('id')}")
            print(f"  文件名: {record.get('filename')}")
            print(f"  下载类型: {record.get('download_type')}")
            print(f"  文件大小: {record.get('file_size')} 字节")
            print(f"  下载时间: {record.get('download_time')}")
            print(f"  是否加密: {record.get('is_encrypted')}")
            print(f"  下载次数: {record.get('download_count')}")
    else:
        print(f"❌ 获取下载记录失败: {records_data.get('error')}")
        return
    
    # 3. 测试分页
    print("\n3. 测试分页功能...")
    page2_response = requests.get(f"{base_url}/download/records?page=2&limit=2", headers=headers)
    
    if page2_response.status_code == 200:
        page2_data = page2_response.json()
        if page2_data.get('success'):
            print(f"✅ 分页测试成功，第2页有 {len(page2_data.get('records', []))} 条记录")
        else:
            print(f"⚠️ 分页测试失败: {page2_data.get('error')}")
    else:
        print(f"⚠️ 分页请求失败: {page2_response.status_code}")
    
    # 4. 测试密码申请记录
    print("\n4. 测试密码申请记录...")
    password_requests_response = requests.get(f"{base_url}/download/password-requests", headers=headers)
    
    if password_requests_response.status_code == 200:
        password_data = password_requests_response.json()
        if password_data.get('success'):
            requests_count = len(password_data.get('requests', []))
            print(f"✅ 密码申请记录获取成功，共 {requests_count} 条记录")
        else:
            print(f"⚠️ 密码申请记录获取失败: {password_data.get('error')}")
    else:
        print(f"⚠️ 密码申请记录请求失败: {password_requests_response.status_code}")
    
    print("\n=== 测试完成 ===")
    print("✅ 下载记录功能已正常工作！")
    print("\n用户现在可以：")
    print("1. 登录系统")
    print("2. 点击左侧菜单的'下载记录'")
    print("3. 查看自己的下载历史记录")
    print("4. 按日期查看分组的记录")
    print("5. 查看密码申请记录")

if __name__ == '__main__':
    test_complete_download_records_flow()
