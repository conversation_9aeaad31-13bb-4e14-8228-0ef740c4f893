#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_download_records_api():
    """测试下载记录API"""
    
    # 首先登录获取token
    login_url = "http://localhost:8086/api/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("正在登录...")
    try:
        login_response = requests.post(login_url, json=login_data)
        print(f"登录响应状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            print(f"登录响应: {json.dumps(login_result, indent=2, ensure_ascii=False)}")
            
            if login_result.get('success'):
                token = login_result['data']['token']
                user_id = login_result['data']['user']['id']
                print(f"登录成功，用户ID: {user_id}, Token: {token[:20]}...")
                
                # 测试下载记录API
                records_url = "http://localhost:8086/api/download/records"
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                print("\n正在获取下载记录...")
                records_response = requests.get(records_url, headers=headers)
                print(f"下载记录响应状态码: {records_response.status_code}")
                
                if records_response.status_code == 200:
                    records_result = records_response.json()
                    print(f"下载记录响应: {json.dumps(records_result, indent=2, ensure_ascii=False)}")
                    
                    if records_result.get('success'):
                        records = records_result.get('records', [])
                        print(f"\n找到 {len(records)} 条下载记录")
                        
                        for i, record in enumerate(records[:3]):  # 只显示前3条
                            print(f"\n记录 {i+1}:")
                            print(f"  ID: {record.get('id')}")
                            print(f"  文件名: {record.get('filename')}")
                            print(f"  下载类型: {record.get('download_type')}")
                            print(f"  文件大小: {record.get('file_size')}")
                            print(f"  下载时间: {record.get('download_time')}")
                            print(f"  是否加密: {record.get('is_encrypted')}")
                    else:
                        print(f"获取下载记录失败: {records_result.get('error')}")
                else:
                    print(f"下载记录API请求失败: {records_response.text}")
            else:
                print(f"登录失败: {login_result.get('error')}")
        else:
            print(f"登录请求失败: {login_response.text}")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")

if __name__ == '__main__':
    test_download_records_api()
